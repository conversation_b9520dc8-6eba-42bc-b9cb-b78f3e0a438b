import { defineStore } from 'pinia'

interface UserInfo {
  uuid: string
  id?: string
  name?: string
  username?: string
  email?: string
  avatar_url?: string
  coins?: number
  level?: number
  exp?: number
  vip_level?: number
  vip_expire_time?: string
  gender?: string
  role?: 'guest' | 'normal' | 'admin'
  status?: string
  plan?: string
  create_time?: string
}

interface Story {
  id: string
  title: string
  description?: string
  preview_url?: string
  carousel_image_url?: string[]
  badge?: string
  status?: 'normal' | 'preparing' | 'admin_only'
  is_subscribed?: boolean
  is_fav?: boolean
  author?: string
  tags?: string[]
  categories?: string[]
  uuid?: string
  cover_url?: string
  image_url?: string
}

interface UserState {
  userId: string | null
  token: string | null
  refreshToken: string | null
  deviceId: string | null
  userInfo: UserInfo | null
  isAuthenticated: boolean
  userPlayedStories: Story[]
  userLikedStories: Story[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userId: null,
    token: null,
    refreshToken: null,
    deviceId: null,
    userInfo: null,
    isAuthenticated: false,
    userPlayedStories: [],
    userLikedStories: [],
  }),

  getters: {
    isGuest: (state) => state.userInfo?.role === 'guest',
    isAdmin: (state) => state.userInfo?.role === 'admin',
    isLoggedIn: (state) =>
      state.isAuthenticated &&
      !!state.token &&
      state.userInfo?.role !== 'guest',
    userCoins: (state) => state.userInfo?.coins || 0,
    userName: (state) =>
      state.userInfo?.name || state.userInfo?.username || 'Guest',
    userAvatar: (state) => state.userInfo?.avatar_url || '',
  },

  actions: {
    setToken(token: string, refreshToken?: string) {
      this.token = token
      if (refreshToken) {
        this.refreshToken = refreshToken
      }
      this.isAuthenticated = true

      // 存储到localStorage - 与 useTokenManager 保持一致
      if (import.meta.client) {
        localStorage.setItem('auth_token', token)
        if (refreshToken) {
          localStorage.setItem('auth_refresh_token', refreshToken)
        }
      }
    },

    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
      this.userId = userInfo.uuid || userInfo.id || null

      // 存储到localStorage - 与 useTokenManager 保持一致
      if (import.meta.client) {
        localStorage.setItem('auth_user_info', JSON.stringify(userInfo))
      }
    },

    setDeviceId(deviceId?: string) {
      if (!deviceId) {
        deviceId =
          'device_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now()
      }
      this.deviceId = deviceId

      if (import.meta.client) {
        localStorage.setItem('deviceId', deviceId)
      }
    },

    logout() {
      this.clearUserState()
      this.syncLogoutToCSR()
    },

    clearUserState() {
      this.token = null
      this.refreshToken = null
      this.userId = null
      this.userInfo = null
      this.isAuthenticated = false
      this.userPlayedStories = []
      this.userLikedStories = []

      if (import.meta.client) {
        localStorage.removeItem('auth_token')
        localStorage.removeItem('auth_refresh_token')
        localStorage.removeItem('auth_user_info')
        localStorage.removeItem('deviceId')
      }
    },

    // 同步 logout 状态到 CSR 应用
    syncLogoutToCSR() {
      if (!import.meta.client) return

      try {
        // 准备清空的认证状态
        const authData = {
          token: null,
          refreshToken: null,
          userId: null,
          isAuthenticated: false,
          isGuest: false,
        }

        // 准备清空的用户状态
        const userData = {
          user: null,
          language: localStorage.getItem('language') || 'en',
          theme: localStorage.getItem('theme') || 'dark',
        }

        // 准备清空的故事状态
        const storyData = {
          currentStory: null,
          currentActor: null,
        }

        // 查找并通知所有iframe中的CSR应用
        const chatIframes = document.querySelectorAll('.chat-iframe')
        const config = useRuntimeConfig()

        console.log('🚪 主应用: 开始同步 logout 状态到 CSR 应用', {
          iframeCount: chatIframes.length,
          targetUrl: config.public.csrAppUrl,
        })

        chatIframes.forEach((iframe, index) => {
          if (iframe instanceof HTMLIFrameElement && iframe.contentWindow) {
            // 发送认证状态同步 - 清空状态
            iframe.contentWindow.postMessage(
              {
                type: 'STATE_SYNC',
                payload: {
                  auth: authData,
                  user: userData,
                  story: storyData,
                },
                timestamp: Date.now(),
                source: 'nuxt-app',
              },
              config.public.csrAppUrl,
            )

            console.log(
              `🚪 主应用: 已向 iframe ${index + 1} 发送 logout 同步消息`,
            )
          }
        })

        console.log('✅ 主应用: logout 状态已同步到所有 CSR 应用')
      } catch (error) {
        console.error('❌ 主应用: logout 状态同步失败:', error)
      }
    },

    async signUpAsGuest() {
      try {
        const { ensureValidToken } = useTokenManager()
        const { token } = await ensureValidToken()

        if (token) {
          this.setToken(token)
          return true
        }

        return false
      } catch (error: unknown) {
        console.error('Guest signup error:', error)
        return false
      }
    },

    async loginWithCode(data: {
      email: string
      code: string
      code_type: string
      gender: string
      user_id?: string
    }) {
      try {
        const config = useRuntimeConfig()
        const response = await $fetch(
          `${config.public.apiBase}/api/v1/user.auth`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: {
              ...data,
              user_id: data.user_id || this.userInfo?.uuid,
            },
          },
        )

        if (response.code !== '0' || !response.data) {
          throw new Error(response.message || 'Login failed')
        }

        const { auth, user } = response.data
        this.setToken(auth.access_token, auth.refresh_token)
        this.setUserInfo(user)

        // 同步到 TokenManager
        if (import.meta.client) {
          const { setTokens, setUserInfo: setTokenManagerUserInfo } =
            useTokenManager()
          setTokens(auth.access_token, auth.refresh_token)
          setTokenManagerUserInfo(user, 'user')
        }

        return true
      } catch (error: unknown) {
        console.error('Login with code error:', error)
        throw error
      }
    },

    async sendVerificationCode(email: string, codeType: string = 'login') {
      try {
        const config = useRuntimeConfig()
        const response = await $fetch(
          `${config.public.apiBase}/api/v1/verify-code.send`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: {
              email,
              code_type: codeType,
            },
          },
        )

        if (response.code !== '0') {
          throw new Error(
            response.message || 'Failed to send verification code',
          )
        }

        return true
      } catch (error: unknown) {
        console.error('Send verification code error:', error)
        throw error
      }
    },

    initFromStorage() {
      if (import.meta.client) {
        const token = localStorage.getItem('auth_token')
        const refreshToken = localStorage.getItem('auth_refresh_token')
        const userInfoStr = localStorage.getItem('auth_user_info')
        const deviceId = localStorage.getItem('deviceId')

        if (token) {
          this.token = token
          this.isAuthenticated = true
        }

        if (refreshToken) {
          this.refreshToken = refreshToken
        }

        if (userInfoStr) {
          try {
            const userInfo = JSON.parse(userInfoStr)
            this.userInfo = userInfo
            this.userId = userInfo?.uuid || userInfo?.id || null
          } catch (error) {
            console.error('Failed to parse user info from storage:', error)
          }
        }

        if (deviceId) {
          this.deviceId = deviceId
        }
      }
    },

    addPlayedStory(storyId: string) {
      if (!this.userPlayedStories.includes(storyId)) {
        this.userPlayedStories.push(storyId)
      }
    },

    addLikedStory(storyId: string) {
      if (!this.userLikedStories.includes(storyId)) {
        this.userLikedStories.push(storyId)
      }
    },

    removeLikedStory(storyId: string) {
      const index = this.userLikedStories.indexOf(storyId)
      if (index > -1) {
        this.userLikedStories.splice(index, 1)
      }
    },

    updateCoins(amount: number) {
      if (this.userInfo) {
        this.userInfo.coins = (this.userInfo.coins || 0) + amount
      }
    },

    // API调用方法
    async getUserInfo() {
      const { fetchUserInfo } = useApi()
      try {
        const response = await fetchUserInfo()
        if (response.code === '0' && response.data) {
          this.setUserInfo(response.data.user)
          return response.data.user
        } else {
          console.error('Failed to fetch user info:', response.message)
          return null
        }
      } catch (error) {
        console.error('Error fetching user info:', error)
        return null
      }
    },

    async getUserPlayedStories() {
      const { fetchUserPlayedStories } = useApi()
      try {
        const response = await fetchUserPlayedStories()
        if (response.code === '0' && response.data) {
          this.userPlayedStories = response.data.stories || []
          return response.data.stories
        } else {
          console.error('Failed to fetch played stories:', response.message)
          return []
        }
      } catch (error) {
        console.error('Error fetching played stories:', error)
        return []
      }
    },

    async getUserLikedStories() {
      const { fetchUserLikedStories } = useApi()
      try {
        const response = await fetchUserLikedStories()
        if (response.code === '0' && response.data) {
          this.userLikedStories = response.data.stories || []
          return response.data.stories
        } else {
          console.error('Failed to fetch liked stories:', response.message)
          return []
        }
      } catch (error) {
        console.error('Error fetching liked stories:', error)
        return []
      }
    },

    async updateUserInfo(data: {
      name?: string
      gender?: string
      avatar_url?: string
    }) {
      const { updateUserInfo: apiUpdateUserInfo } = useApi()
      try {
        const response = await apiUpdateUserInfo(data)
        if (response.code === '0' && response.data) {
          this.setUserInfo(response.data.user)
          return response.data.user
        } else {
          console.error('Failed to update user info:', response.message)
          throw new Error(response.message || 'Failed to update user info')
        }
      } catch (error) {
        console.error('Error updating user info:', error)
        throw error
      }
    },
  },

  persist: {
    key: 'user-store',
    pick: [
      'userId',
      'token',
      'refreshToken',
      'deviceId',
      'userInfo',
      'isAuthenticated',
    ],
  } as any,
})
